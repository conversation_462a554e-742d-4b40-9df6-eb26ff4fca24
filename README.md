# 🕰️ a-step-too-far

A lightweight test management tool built with Django.

The goal: give testers a simple, no-fluff interface to create, edit, and view test cases with steps, expected results, and test data. All without needing to wade through bloated enterprise tools.

## 💡 Features (MVP)

- Create and manage test cases
- Add structured test steps with:
  - Action
  - Expected Result
  - Test Data
- Organize with priorities, status, and tags
- Fully usable via Django Admin
- SQLite-backed for easy local development

Planned future features:
- CSV/JSON bulk import
- REST API (via Django REST Framework)
- Integration with Robot Framework test files
- Suggestions from test code changes

---

## 🚀 Getting Started

### 1. Clone the project

```bash
git clone https://github.com/maymai/a-step-too-far.git
cd a-step-too-far
