# yaml-language-server: $schema=https://coderabbit.ai/integrations/schema.v2.json
language: en-GB
tone_instructions: ''
early_access: true
enable_free_tier: true
reviews:
  profile: chill
  request_changes_workflow: true
  high_level_summary: true
  high_level_summary_placeholder: '@coderabbitai summary'
  auto_title_placeholder: '@coderabbitai'
  review_status: true
  poem: true
  collapse_walkthrough: false
  sequence_diagrams: true
  changed_files_summary: true
  labeling_instructions: []
  path_filters:
    - '!client/dist/'
    - '!client/src/**/*.es6'
    - '!client/src/**/*.html'
    - '!client/src/vendor/'
  path_instructions:
    - path: '**/**'
      instructions: |
        The global rules for all files in the repo are as follows:
        - Ensure there are no unused imports or variables and ensure they are
          properly defined in the code base.
        - Avoid code duplication and provide a reference to where similar code
          could be found to encourage reusability.
    - path: 'client/src/**'
      instructions: |
        The folder structure rules for the repository are as follows:
        - Imports to global / shared folders are prefixed with the @ symbol.
        - If a file within these folders imports another file or folder from
          another of these folders, ensure absolute imports are used.
        - If a file within these folders imports another file or folder within
          the same folder, ensure relative imports are used.
        - We have agreed as a team on a modular folder structure that is easy to maintain and understand, given the technologies that we use.
          AIMS OF THIS FOLDER STRUCTURE
          The aim of having this folder structure is to ensure the following:
          - Stop mixing file types
          - Stop mixing dynamic and static folders
          - Follow the same folder structure everywhere, and ensure consistency between squads
          - Group files if needed
          - Use index.js file to indicate what the module/component/entity exports
          Given the above considerations, we have come up with the following folder structure rules.
          GLOBAL / SHARED
          The global hooks, hoc, contexts etc should be stored on the same root level as the global components.
          WHAT IS A MODULE?
          A module in the context of our folder structure rules is a complex entity that lives in one of our top level folders such as @components, @pages etc. This is not to be confused with a Base component, which will be described in the next section.
          A module will typically contain nested folders like utils, components, hooks etc in it as well as index.js, module.jsx and module.styled.jsx files.
          Note: we should not have nested modules, and a module can have only one level of the components, utils, hooks, etc. folders.
          BASE COMPONENT
          A base component is stored under the /components directory of a module, and it is simple enough such that it doesn't have any nested directories in it, because we store all related files in the /components, /utils etc directories of the module. It can have only the following files in it:
          - index.js
          - component.jsx
          - component.styled.jsx
          MODULE COMPONENT
          A module component is a shared component that lives under the @components directory, and since it is a module, it can have nested utils/, components/ etc. Please note, that the module- prefix is added just for demonstration.
          PAGES
          A page is also a module, and so it can have nested utils/, components/ etc.
        - At Adthena we use British English for variable and function names in our JS code, with the following exception:
          for color we use the US spelling to keep consistency with the CSS property.
        - Unit tests should be stored in a folder named __tests__ in the root directory of the modules that they test.
    - path: 'client/src/**/__tests__/**'
      instructions: |
        Assess the unit test code employing the Jest testing framework, Ensure
        that:
        - The code adheres to best practices associated with Jest and React
        testing library (where applicable).
        - React components should additionally be tested with React testing
        library and have snapshot tests where applicable.
        - Test descriptions must be sufficiently detailed to clarify the purpose
        of each test.
        - Test files that do not live in the correct folder should be moved.
        - Unit test filenames should follow this *.test.js(x) naming convention.
        - Avoid importing from an index file directly, import the file that's
          being tested without going through the index. This is to ensure better readability.
    - path: 'client/**/hooks/**'
      instructions: |
        Assess the code employing hooks. Ensure that:
        - The code adheres to best practices associated with hooks.
        - Hooks should be used in a way that is easy to understand and maintain.
        - Hooks that utilise useFetch already implement exception handling, and
          would be exposed by the error variable returned.
        - Hooks should be commented with JSDoc to include the parameters and return types
        where applicable.
    - path: 'client/**/components/**'
      instructions: |
        Assess the code employing React components. Ensure that:
        - The code adheres to best practices associated with React components.
        - Components should be used in a way that is easy to understand and maintain.
        - Prefer composability over God implementations.
        - It follows SOLID principles.
        - Ensure separation of concerns and keep knowledge in the right place.
        - Minimise unnecessary re-rendering.
        - Consider non-functional requirements such as performance optimisation, readability and maintainability.
        - We currently do not care about accessibility.
        - Ensure prop types are used correctly.
        - Ensure React code is indented correctly, especially when using JSX
        expressions.
    - path: 'client/**/constants/**'
      instructions: |
        When using constants for JavaScript or TypeScript ensure that:
        - They use uppercase and snake case naming conventions.
        - They are easy to understand.
    - path: 'client/src/themes/**'
      instructions: |
        This folder contains theme related code that is used by Styled
        Components. Ensure that:
        - Colours follow good naming conventions that are easy to understand.
        For reference, we use Figma as our design system.
        - There are no duplicate colours.
        - Themes adhere to best practices.
    - path: 'client/**/**/.js'
      instructions: |
        Assess the code employing JavaScript. Ensure that:
        - The code adheres to best practices associated with JavaScript.
        - JavaScript should be used in a way that is easy to understand and maintain.
        - JSDoc should be added to JavaScript functions that contain parameter and return types.
    - path: 'client/**/**/*.styled.jsx'
      instructions: |
        Assess the code employing Styled Components. Ensure that:
        - The code adheres to best practices associated with Styled Components.
        - Styled Components should be used in a way that is easy to understand and maintain.
        - Prefer to use composition over inheritance.
        - It should follow our stylelint rules.
        - All Styled Components should exist in a file that follows the pattern
          of this rule, they should not be inside a file that is used to render a React component.
        - Colours are not hard-coded and should use the theme colours via the ThemeProvider to maintain a single source of truth.
        - Global concerns should not exist in a styled component, and should be considered to be moved to global styles.
  abort_on_close: true
  auto_review:
    enabled: true
    auto_incremental_review: true
    ignore_title_keywords: []
    labels: []
    drafts: false
    base_branches:
      - dev
      - epic-.*
      - feature-.*
  tools:
    shellcheck:
      enabled: true
    ruff:
      enabled: true
    markdownlint:
      enabled: true
    github-checks:
      enabled: true
      timeout_ms: 90000
    languagetool:
      enabled: true
      enabled_only: false
      level: default
    biome:
      enabled: true
    hadolint:
      enabled: true
    swiftlint:
      enabled: false
    phpstan:
      enabled: false
      level: default
    golangci-lint:
      enabled: false
    yamllint:
      enabled: true
    gitleaks:
      enabled: true
    checkov:
      enabled: true
    detekt:
      enabled: false
    eslint:
      enabled: true
    rubocop:
      enabled: false
    buf:
      enabled: true
    regal:
      enabled: false
    actionlint:
      enabled: true
    pmd:
      enabled: true
    cppcheck:
      enabled: false
    semgrep:
      enabled: true
    circleci:
      enabled: true
chat:
  auto_reply: true
knowledge_base:
  opt_out: false
  learnings:
    scope: local
  issues:
    scope: auto
  jira:
    project_keys:
      - ARCH
      - BA
      - BP
      - FBI
      - FOD
      - OPS
      - PLATFORM
      - SPS
  linear:
    team_keys: []
  pull_requests:
    scope: local
