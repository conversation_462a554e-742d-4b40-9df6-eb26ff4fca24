import nested_admin
from django.contrib import admin
from django import forms
from .models import TestCase, TestStep, ExpectedResult

# ---------------------------
# Admin form with bulk fields
# ---------------------------
class TestCaseAdminForm(forms.ModelForm):
    bulk_steps = forms.CharField(widget=forms.Textarea, required=False, label="Bulk Test Steps")
    bulk_results = forms.CharField(widget=forms.Textarea, required=False, label="Bulk Expected Results")

    class Meta:
        model = TestCase
        fields = '__all__'


# -------------------------------
# Nested inlines for each level
# -------------------------------
class ExpectedResultInline(nested_admin.NestedTabularInline):
    model = ExpectedResult
    extra = 1

class TestStepInline(nested_admin.NestedTabularInline):
    model = TestStep
    extra = 1
    inlines = [ExpectedResultInline]  # ✅ This is the missing line


# ------------------------
# Main admin for TestCases
# ------------------------
class TestCaseAdmin(nested_admin.NestedModelAdmin):
    form = TestCaseAdminForm
    inlines = [TestStepInline]
    list_display = ('title', 'priority', 'status', 'automation', 'created_by', 'created_at')
    search_fields = ('title', 'description', 'test_data')
    list_filter = ('priority', 'status', 'automation')
    ordering = ['-created_at']
    exclude = ('created_by',)

    def save_model(self, request, obj, form, change):
        if not obj.created_by:
            obj.created_by = request.user
        obj.save()

        # Handle bulk steps
        bulk_steps = form.cleaned_data.get('bulk_steps', '')
        if bulk_steps:
            self._import_steps_from_text(obj, bulk_steps)

        # Handle bulk expected results
        bulk_results = form.cleaned_data.get('bulk_results', '')
        if bulk_results:
            self._import_expected_results_from_text(obj, bulk_results)

    # -------------------------------
    # Parser for bulk step input
    # -------------------------------
    def _import_steps_from_text(self, test_case, raw):
        import re
        from .models import TestStep

        # Clear existing steps
        TestStep.objects.filter(test_case=test_case).delete()

        lines = [l.strip() for l in raw.strip().splitlines() if l.strip()]
        for line in lines:
            match = re.match(r'^(\d+)\.\s+(.*)$', line)
            if match:
                number = int(match.group(1))
                text = match.group(2)
                TestStep.objects.create(test_case=test_case, step_number=number, action=text)

    # ---------------------------------------
    # Parser for bulk expected result input
    # ---------------------------------------
    def _import_expected_results_from_text(self, test_case, raw):
        import re
        from .models import TestStep, ExpectedResult

        # Clear previous expected results
        ExpectedResult.objects.filter(test_step__test_case=test_case).delete()

        # Map step_number to TestStep objects
        step_map = {s.step_number: s for s in test_case.steps.all()}

        lines = [l.strip() for l in raw.strip().splitlines() if l.strip()]
        for line in lines:
            match = re.match(r'^(\d+(?:\.\d+)?)\s+(.*)$', line)
            if match:
                result_num = match.group(1)
                text = match.group(2)
                step_num = int(result_num.split('.')[0])
                step = step_map.get(step_num)
                if step:
                    ExpectedResult.objects.create(
                        test_step=step,
                        result_number=result_num,
                        text=text
                    )

    class Media:
        css = {
            'all': ('testcases/admin.css',)
        }

# Register the whole tree
admin.site.register(TestCase, TestCaseAdmin)