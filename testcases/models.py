from django.db import models
from django.contrib.auth.models import User


class TestCase(models.Model):
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('READY', 'Ready'),
        ('NEEDS_UPDATING', 'Needs Updating'),
        ('DEPRECATED', 'Deprecated'),
    ]

    AUTOMATION_CHOICES = [
        ('CANNOT_AUTOMATE', 'Cannot automate'),
        ('NOT_AUTOMATED', 'Not automated'),
        ('ROBOT_FRAMEWORK', 'Robot Framework'),
        ('UNIT_TESTS', 'Unit Tests'),
        ('SNAPSHOT', 'Snapshot'),
    ]

    title = models.CharField(max_length=200)
    section = models.CharField(max_length=100, blank=True)  # Optional grouping
    description = models.TextField(blank=True)
    preconditions = models.TextField(blank=True)
    test_data = models.TextField(blank=True)
    automation = models.CharField(max_length=20, choices=AUTOMATION_CHOICES, default='NOT_AUTOMATED')

    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='DRAFT')

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_testcases')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


class TestStep(models.Model):
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE, related_name='steps')
    step_number = models.PositiveIntegerField()  # For ordering: 1, 2, 3
    action = models.TextField()

    class Meta:
        ordering = ['step_number']
        constraints = [
            models.UniqueConstraint(
                fields=['test_case', 'step_number'],
                name='unique_step_number_per_testcase'
            )
        ]

    def __str__(self):
        return f"Step {self.step_number} for {self.test_case.title}"


class ExpectedResult(models.Model):
    test_step = models.ForeignKey(TestStep, on_delete=models.CASCADE, related_name='expected_results')
    result_number = models.CharField(max_length=10)  # Allows "1.1", "1.2", "2", etc.
    text = models.TextField()

    class Meta:
        ordering = ['result_number']

    def __str__(self):
        return f"Expected {self.result_number} for Step {self.test_step.step_number}"