# Generated by Django 5.2.3 on 2025-06-13 11:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testcases', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='teststep',
            name='expected_result',
        ),
        migrations.RemoveField(
            model_name='teststep',
            name='test_data',
        ),
        migrations.AddField(
            model_name='testcase',
            name='automation',
            field=models.CharField(choices=[('CANNOT_AUTOMATE', 'Cannot automate'), ('NOT_AUTOMATED', 'Not automated'), ('ROBOT_FRAMEWORK', 'Robot Framework'), ('UNIT_TESTS', 'Unit Tests'), ('SNAPSHOT', 'Snapshot')], default='NOT_AUTOMATED', max_length=20),
        ),
        migrations.AddField(
            model_name='testcase',
            name='preconditions',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='testcase',
            name='section',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='testcase',
            name='test_data',
            field=models.TextField(blank=True),
        ),
        migrations.CreateModel(
            name='ExpectedResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('result_number', models.CharField(max_length=10)),
                ('text', models.TextField()),
                ('test_step', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expected_results', to='testcases.teststep')),
            ],
            options={
                'ordering': ['result_number'],
            },
        ),
    ]
